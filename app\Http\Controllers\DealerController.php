<?php

namespace App\Http\Controllers;

use App\Models\Dealer;
use App\Models\Region;
use Illuminate\Http\Request;
use App\Http\Requests\StoreDealerRequest;
use App\Http\Requests\UpdateDealerRequest;

class DealerController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $perPage = request('perPage', 10);
        $dealers = Dealer::with(['region', 'customers'])
            ->search(request('q'))
            ->orderBy('id', 'desc')
            ->paginate($perPage)
            ->appends(request()->query());

        return view('dealers.index', compact('dealers'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $regions = Region::active()->orderBy('name')->get();
        return view('dealers.create', compact('regions'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreDealerRequest $request)
    {
        $validated = $request->validated();
        $validated['status'] = $request->has('status');

        Dealer::create($validated);

        return redirect()->route('dealers.index')->with('success', 'Bayi başarıyla eklendi!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Dealer $dealer)
    {
        $dealer->load(['region', 'customers']);
        return view('dealers.show', compact('dealer'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Dealer $dealer)
    {
        $regions = Region::active()->orderBy('name')->get();
        return view('dealers.edit', compact('dealer', 'regions'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateDealerRequest $request, Dealer $dealer)
    {
        $validated = $request->validated();
        $validated['status'] = $request->has('status');

        $dealer->update($validated);

        return redirect()->route('dealers.index')->with('success', 'Bayi başarıyla güncellendi!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Dealer $dealer)
    {
        // Bayiye bağlı müşteri varsa silme
        if ($dealer->customers()->count() > 0) {
            return redirect()->route('dealers.index')->with('error', 'Bu bayiye bağlı müşteriler bulunduğu için silinemez!');
        }

        $dealer->delete();

        return redirect()->route('dealers.index')->with('success', 'Bayi başarıyla silindi!');
    }

    /**
     * DataTable AJAX endpoint for dealers
     */
    public function datatable(Request $request)
    {
        $query = Dealer::with(['region', 'customers']);

        // Global search
        if ($request->has('search') && $request->search['value']) {
            $searchValue = $request->search['value'];
            $query->where(function ($q) use ($searchValue) {
                $q->where('name', 'ILIKE', "%{$searchValue}%")
                  ->orWhere('contact_person', 'ILIKE', "%{$searchValue}%")
                  ->orWhere('phone', 'ILIKE', "%{$searchValue}%")
                  ->orWhere('email', 'ILIKE', "%{$searchValue}%")
                  ->orWhere('city', 'ILIKE', "%{$searchValue}%")
                  ->orWhere('district', 'ILIKE', "%{$searchValue}%")
                  ->orWhereHas('region', function ($regionQuery) use ($searchValue) {
                      $regionQuery->where('name', 'ILIKE', "%{$searchValue}%");
                  });
            });
        }

        // Column-specific search
        if ($request->has('columns')) {
            foreach ($request->columns as $index => $column) {
                if (isset($column['search']['value']) && $column['search']['value'] !== '') {
                    $searchValue = $column['search']['value'];

                    switch ($index) {
                        case 0: // ID
                            $query->where('id', $searchValue);
                            break;
                        case 1: // Name
                            $query->where('name', 'ILIKE', "%{$searchValue}%");
                            break;
                        case 2: // Region
                            $query->whereHas('region', function ($q) use ($searchValue) {
                                $q->where('name', 'ILIKE', "%{$searchValue}%");
                            });
                            break;
                        case 3: // Contact Person
                            $query->where('contact_person', 'ILIKE', "%{$searchValue}%");
                            break;
                        case 4: // Phone
                            $query->where('phone', 'ILIKE', "%{$searchValue}%");
                            break;
                        case 5: // Email
                            $query->where('email', 'ILIKE', "%{$searchValue}%");
                            break;
                        case 6: // City
                            $query->where('city', 'ILIKE', "%{$searchValue}%");
                            break;
                        case 7: // District
                            $query->where('district', 'ILIKE', "%{$searchValue}%");
                            break;
                        case 8: // Customers Count
                            $query->has('customers', '>=', (int)$searchValue);
                            break;
                        case 9: // Status
                            if (strtolower($searchValue) === 'aktif' || $searchValue === '1') {
                                $query->where('status', true);
                            } elseif (strtolower($searchValue) === 'pasif' || $searchValue === '0') {
                                $query->where('status', false);
                            }
                            break;
                    }
                }
            }
        }

        // Ordering
        if ($request->has('order')) {
            $orderColumn = $request->order[0]['column'];
            $orderDir = $request->order[0]['dir'];

            switch ($orderColumn) {
                case 0:
                    $query->orderBy('id', $orderDir);
                    break;
                case 1:
                    $query->orderBy('name', $orderDir);
                    break;
                case 2:
                    $query->join('regions', 'dealers.region_id', '=', 'regions.id')
                          ->orderBy('regions.name', $orderDir)
                          ->select('dealers.*');
                    break;
                case 3:
                    $query->orderBy('contact_person', $orderDir);
                    break;
                case 4:
                    $query->orderBy('phone', $orderDir);
                    break;
                case 5:
                    $query->orderBy('email', $orderDir);
                    break;
                case 6:
                    $query->orderBy('city', $orderDir);
                    break;
                case 7:
                    $query->orderBy('district', $orderDir);
                    break;
                case 9:
                    $query->orderBy('status', $orderDir);
                    break;
                case 10:
                    $query->orderBy('created_at', $orderDir);
                    break;
                default:
                    $query->orderBy('id', 'desc');
                    break;
            }
        } else {
            $query->orderBy('id', 'desc');
        }

        // Get total count before pagination
        $totalRecords = Dealer::count();
        $filteredRecords = $query->count();

        // Pagination
        $start = $request->start ?? 0;
        $length = $request->length ?? 10;
        $dealers = $query->skip($start)->take($length)->get();

        $data = [];
        foreach ($dealers as $dealer) {
            $statusBadge = $dealer->status
                ? '<span class="badge badge-success">Aktif</span>'
                : '<span class="badge badge-secondary">Pasif</span>';

            $actions = '
                <div class="btn-group" role="group">
                    <a href="' . route('dealers.show', $dealer->id) . '" class="btn btn-sm btn-info" title="Görüntüle">
                        <i class="fas fa-eye"></i>
                    </a>
                    <a href="' . route('dealers.edit', $dealer->id) . '" class="btn btn-sm btn-warning" title="Düzenle">
                        <i class="fas fa-edit"></i>
                    </a>
                    <form action="' . route('dealers.destroy', $dealer->id) . '" method="POST" style="display: inline;"
                          onsubmit="return confirm(\'Bu bayiyi silmek istediğinizden emin misiniz?\')">
                        ' . csrf_field() . '
                        ' . method_field('DELETE') . '
                        <button type="submit" class="btn btn-sm btn-danger" title="Sil">
                            <i class="fas fa-trash"></i>
                        </button>
                    </form>
                </div>';

            $data[] = [
                $dealer->id,
                $dealer->name,
                $dealer->region->name ?? '-',
                $dealer->contact_person ?? '-',
                $dealer->phone ?? '-',
                $dealer->email ?? '-',
                $dealer->city ?? '-',
                $dealer->district ?? '-',
                $dealer->customers->count(),
                $statusBadge,
                $dealer->created_at->format('d.m.Y H:i'),
                $actions
            ];
        }

        return response()->json([
            'draw' => intval($request->draw),
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $filteredRecords,
            'data' => $data
        ]);
    }
}
