@extends('layouts.index')

@section('content')
<!-- Content Header (Page header) -->
<section class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1>Tüm Müşteri Takip Kayıtları</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Ana Sayfa</a></li>
                    <li class="breadcrumb-item active">Takip Kayıtları</li>
                </ol>
            </div>
        </div>
    </div><!-- /.container-fluid -->
</section>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <a href="{{ route('customers.index') }}" class="btn btn-secondary">Müşteri Listesi</a>
                        </h3>

                        <div class="card-tools">
                            @if(session('success'))
                                <div class="alert alert-success">
                                    {{ session('success') }}
                                </div>
                            @endif


                        </div>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body table-responsive p-0" style="height: 100%;">
                        <table id="allFollowupsTable" class="table table-head-fixed text-nowrap table-bordered table-striped">
                            <thead>
                            <tr>
                                <th>ID</th>
                                <th>Müşteri</th>
                                <th>Takip Tarihi</th>
                                <th>Görüşme Tarihi</th>
                                <th>Durum</th>
                                <th>Görüşme Türü</th>
                                <th>Çalışma Türü</th>
                                <th>Ziyaret Eden</th>
                                <th>Şehir</th>
                                <th>İlçe</th>
                                <th>Not</th>
                                <th>Anlaşma</th>
                                <th>PlusCard Yükleme</th>
                                <th>İşlemler</th>
                            </tr>
                            </thead>
                         
                        </table>
                    </div>
                    <!-- /.card-body -->
                    <!-- /.card-footer -->
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>
<!-- /.content -->
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    $('#allFollowupsTable').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "{{ route('customer-followups.all.datatable') }}",
            "type": "GET"
        },
        "language": {
            "url": "https://cdn.datatables.net/plug-ins/1.13.7/i18n/tr.json"
        },
        "pageLength": 25,
        "lengthMenu": [[10, 25, 50, 100], [10, 25, 50, 100]],
        "order": [[0, "desc"]],
        "columns": [
            { "data": 0, "name": "id" },
            { "data": 1, "name": "customer" },
            { "data": 2, "name": "track_date" },
            { "data": 3, "name": "meet_date" },
            { "data": 4, "name": "status" },
            { "data": 5, "name": "conversation_type" },
            { "data": 6, "name": "work_type" },
            { "data": 7, "name": "user_name" },
            { "data": 8, "name": "city" },
            { "data": 9, "name": "district" },
            { "data": 10, "name": "note" },
            { "data": 11, "name": "agreement_status" },
            { "data": 12, "name": "pluscard_been_loaded" },
            { "data": 13, "name": "actions", "orderable": false, "searchable": false }
        ],
        "initComplete": function () {
            // Her sütun için filtreleme input'u ekle
            this.api().columns().every(function (index) {
                var column = this;
                var title = $(column.header()).text();

                // İşlemler sütunu için filtreleme ekleme
                if (index === $('#allFollowupsTable thead th').length - 1) {
                    return;
                }

                // Header'a filtreleme input'u ekle
                var input = $('<input type="text" placeholder="' + title + ' ara..." class="form-control form-control-sm" style="margin-top: 5px;" />')
                    .appendTo($(column.header()))
                    .on('keyup change clear', function () {
                        if (column.search() !== this.value) {
                            column.search(this.value).draw();
                        }
                    });
            });
        }
    });
});
</script>
@endpush