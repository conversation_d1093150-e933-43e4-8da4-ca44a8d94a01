@extends('layouts.index')

@section('content')
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h2>{{ $customer->company_name }} - <PERSON><PERSON><PERSON></h2>
        <a href="{{ route('customers.customer-followups.create', $customer->id) }}" class="btn btn-primary"><PERSON><PERSON></a>
    </div>
    @if(session('success'))
        <div class="alert alert-success">{{ session('success') }}</div>
    @endif
    <table id="customerFollowupsTable" class="table table-bordered table-striped">
        <thead>
            <tr>
                <th>ID</th>
                <th><PERSON><PERSON><PERSON></th>
                <th>Durum</th>
                <th>Not</th>
                <th><PERSON><PERSON><PERSON><PERSON></th>
                <th><PERSON><PERSON><PERSON><PERSON></th>
            </tr>
        </thead>
        <tbody>
            <!-- Server-side DataTable ile doldurulacak -->
        </tbody>
    </table>
    <a href="{{ route('customers.index') }}" class="btn btn-secondary mt-3">Müşteri Listesine Dön</a>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    $('#customerFollowupsTable').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "{{ route('customers.customer-followups.datatable', $customer->id) }}",
            "type": "GET"
        },
        "language": {
            "url": "https://cdn.datatables.net/plug-ins/1.13.7/i18n/tr.json"
        },
        "pageLength": 25,
        "lengthMenu": [[10, 25, 50, 100], [10, 25, 50, 100]],
        "order": [[0, "desc"]],
        "columns": [
            { "data": 0, "name": "id" },
            { 
                "data": 1, 
                "name": "track_date",
                "render": function (data, type, row) {
                    if (!data) return '';
                    const date = new Date(data);
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    return `${year}-${month}-${day}`;
                }
            },
            { "data": 2, "name": "status" },
            { "data": 3, "name": "note" },
            { "data": 4, "name": "agreement_status" },
            { "data": 5, "name": "actions", "orderable": false, "searchable": false }
        ],
        "initComplete": function () {
            // Her sütun için filtreleme input'u ekle
            this.api().columns().every(function (index) {
                var column = this;
                var title = $(column.header()).text();

                // İşlemler sütunu için filtreleme ekleme
                if (index === $('#customerFollowupsTable thead th').length - 1) {
                    return;
                }

                // Header'a filtreleme input'u ekle
                var input = $('<input type="text" placeholder="' + title + ' ara..." class="form-control form-control-sm" style="margin-top: 5px;" />')
                    .appendTo($(column.header()))
                    .on('keyup change clear', function () {
                        if (column.search() !== this.value) {
                            column.search(this.value).draw();
                        }
                    });
            });
        }
    });
});
</script>
@endpush