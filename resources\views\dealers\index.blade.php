@extends('layouts.index')

@section('content')
<!-- Content Header (Page header) -->
<section class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1>Bayi <PERSON>esi</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Ana Sayfa</a></li>
                    <li class="breadcrumb-item active">Bayi Listesi</li>
                </ol>
            </div>
        </div>
    </div><!-- /.container-fluid -->
</section>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <a href="{{ route('dealers.create') }}" class="btn btn-primary"><PERSON><PERSON><PERSON></a>
                        </h3>

                        <div class="card-tools">
                            @if(session('success'))
                                <div class="alert alert-success">
                                    {{ session('success') }}
                                </div>
                            @endif

                            <form method="GET" action="" class="d-flex">
                                <div class="input-group input-group-sm">
                                    <input type="text" name="q" class="form-control float-right" placeholder="bayi adı, şehir, telefon" value="{{ request('q') }}">

                                    <div class="input-group-append">
                                        <button type="submit" class="btn btn-default"><i class="fas fa-search"></i></button>
                                    </div>
                                </div>
                                @if(request('q'))
                                    <a href="{{ route('dealers.index') }}" class="btn btn-outline-danger">Temizle</a>
                                @endif
                            </form>
                        </div>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body table-responsive p-0" style="height: 100%;">
                        <table id="dealersTable" class="table table-head-fixed text-nowrap table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Bayi Adı</th>
                                    <th>Bölge</th>
                                    <th>İletişim Kişisi</th>
                                    <th>Telefon</th>
                                    <th>E-posta</th>
                                    <th>Şehir</th>
                                    <th>İlçe</th>
                                    <th>Müşteri Sayısı</th>
                                    <th>Durum</th>
                                    <th>Kayıt Tarihi</th>
                                    <th>İşlemler</th>
                                </tr>
                            </thead>
                            <tbody>
                            @foreach($dealers as $dealer)
                                <tr>
                                    <td>{{ $dealer->id }}</td>
                                    <td>{{ $dealer->name }}</td>
                                    <td>{{ $dealer->region->name ?? '-' }}</td>
                                    <td>{{ $dealer->contact_person ?? '-' }}</td>
                                    <td>{{ $dealer->phone ?? '-' }}</td>
                                    <td>{{ $dealer->email ?? '-' }}</td>
                                    <td>{{ $dealer->city ?? '-' }}</td>
                                    <td>{{ $dealer->district ?? '-' }}</td>
                                    <td>{{ $dealer->customers_count ?? $dealer->customers->count() }}</td>
                                    <td>
                                        @if($dealer->status)
                                            <span class="badge badge-success">Aktif</span>
                                        @else
                                            <span class="badge badge-danger">Pasif</span>
                                        @endif
                                    </td>
                                    <td>{{ $dealer->created_at->format('d.m.Y') }}</td>
                                    <td>
                                        <a href="{{ route('dealers.show', $dealer->id) }}" class="btn btn-info btn-sm" title="Detay">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('dealers.edit', $dealer->id) }}" class="btn btn-warning btn-sm" title="Düzenle">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="{{ route('dealers.destroy', $dealer->id) }}" method="POST" style="display: inline-block;" onsubmit="return confirm('Bu bayiyi silmek istediğinizden emin misiniz?')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-danger btn-sm" title="Sil">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                    </div>
                    <!-- /.card-body -->
                    <div class="card-footer clearfix">
                        {{ $dealers->links('pagination::bootstrap-4') }}
                    </div>
                </div>
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>
<!-- /.content -->
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    $('#dealersTable').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "{{ route('dealers.datatable') }}",
            "type": "GET"
        },
        "language": {
            "url": "https://cdn.datatables.net/plug-ins/1.13.7/i18n/tr.json"
        },
        "pageLength": 25,
        "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "Tümü"]],
        "order": [[0, "desc"]],
        "scrollX": true,
        "columnDefs": [
            {
                "targets": -1, // Son sütun (İşlemler)
                "orderable": false,
                "searchable": false
            }
        ],
        "initComplete": function () {
            // Her sütun için filtreleme input'u ekle
            this.api().columns().every(function (index) {
                var column = this;
                var title = $(column.header()).text();

                // İşlemler sütunu için filtreleme ekleme
                if (index === $('#dealersTable thead th').length - 1) {
                    return;
                }

                // Header'a filtreleme input'u ekle
                var input = $('<input type="text" placeholder="' + title + ' ara..." class="form-control form-control-sm" style="margin-top: 5px;" />')
                    .appendTo($(column.header()))
                    .on('keyup change clear', function () {
                        if (column.search() !== this.value) {
                            column.search(this.value).draw();
                        }
                    });
            });
        }
    });
});
</script>
@endpush
