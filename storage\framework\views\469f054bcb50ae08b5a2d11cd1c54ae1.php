<?php $__env->startSection('content'); ?>
<!-- Content Header (Page header) -->
<section class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1>Potansiyel Müşteri Listesi</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">Ana Sayfa</a></li>
                    <li class="breadcrumb-item active">Potansiyel Müşteri Listesi</li>
                </ol>
            </div>
        </div>
    </div><!-- /.container-fluid -->
</section>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <a href="<?php echo e(route('potential-customers.create')); ?>" class="btn btn-primary">Yeni Potansiyel Müşteri</a>
                        </h3>

                        <div class="card-tools">
                            <?php if(session('success')): ?>
                                <div class="alert alert-success">
                                    <?php echo e(session('success')); ?>

                                </div>
                            <?php endif; ?>

                            <form method="GET" action="" class="d-flex">
                                <div class="input-group input-group-sm">
                                    <input type="text" name="q" class="form-control float-right" placeholder="telefon,isim,email,şirket" value="<?php echo e(request('q')); ?>">

                                    <div class="input-group-append">
                                        <button type="submit" class="btn btn-default"><i class="fas fa-search"></i></button>
                                    </div>
                                </div>
                                <?php if(request('q')): ?>
                                    <a href="<?php echo e(route('potential-customers.index')); ?>" class="btn btn-outline-danger">Temizle</a>
                                <?php endif; ?>
                            </form>
                        </div>
                    </div>
                    <!-- /.card-header -->

                    <div class="card-body table-responsive p-0" style="height: 100%;">

                        <table id="potentialCustomersTable" class="table table-head-fixed text-nowrap table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Firma Adı</th>
                                    <th>Yetkili İsim</th>
                                    <th>Yetkili Soyisim</th>
                                    <th>Şirket Telefonları</th>
                                    <th>Şehir</th>
                                    <th>İlçe</th>
                                    <th>Teklif Durumu</th>
                                    <th>Teklif Tarihi</th>
                                    <th>İşlemler</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $potentialCustomers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><?php echo e($customer->id); ?></td>
                                        <td><?php echo e($customer->company_name); ?></td>
                                        <td>
                                            <?php if($customer->authorizedPersons && $customer->authorizedPersons->count() > 0): ?>
                                                <?php echo e($customer->authorizedPersons->first()->name ?? '-'); ?>

                                            <?php else: ?>
                                                -
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($customer->authorizedPersons && $customer->authorizedPersons->count() > 0): ?>
                                                <?php echo e($customer->authorizedPersons->first()->lastname ?? '-'); ?>

                                            <?php else: ?>
                                                -
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php
                                                $allPhones = collect();

                                                if($customer->phones && $customer->phones->count() > 0) {
                                                    foreach($customer->phones as $phone) {
                                                        $allPhones->push($phone->phone . ' (' . $phone->type . ')');
                                                    }
                                                }
                                            ?>

                                            <?php if($allPhones->count() > 0): ?>
                                                <?php $__currentLoopData = $allPhones; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $phone): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <small class="d-block">
                                                        <span class="badge badge-primary me-1"><?php echo e($loop->iteration); ?></span>
                                                        <?php echo e($phone); ?>

                                                    </small>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php else: ?>
                                                <small class="text-muted">Telefon yok</small>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo e($customer->city); ?></td>
                                        <td><?php echo e($customer->district); ?></td>
                                        <td>
                                            <span class="badge badge-<?php echo e($customer->offer_status == 'kabul' ? 'success' : ($customer->offer_status == 'ret' ? 'danger' : ($customer->offer_status == 'müşteri' ? 'primary' : 'secondary'))); ?>">
                                                <?php echo e($customer->offer_status_display); ?>

                                            </span>
                                        </td>
                                        <td><?php echo e($customer->offer_date); ?></td>
                                        <td>
                                            <a href="<?php echo e(route('potential-customers.show', $customer->id)); ?>" class="btn btn-info btn-sm">Detay</a>
                                            <a href="<?php echo e(route('potential-customers.edit', $customer->id)); ?>" class="btn btn-warning btn-sm">Düzenle</a>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                    <!-- /.card-body -->
                    <div class="card-footer clearfix">
                        <?php echo e($potentialCustomers->links('pagination::bootstrap-4')); ?>

                    </div>
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>
<!-- /.content -->
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    $('#potentialCustomersTable').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "<?php echo e(route('potential-customers.datatable')); ?>",
            "type": "GET"
        },
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.13.7/i18n/tr.json"
        },
        "pageLength": 25,
        "lengthMenu": [[10, 25, 50, 100], [10, 25, 50, 100]],
        "order": [[0, "desc"]],
        "columns": [
            { "data": 0, "name": "id" },
            { "data": 1, "name": "company_name" },
            { "data": 2, "name": "authorized_name" },
            { "data": 3, "name": "authorized_lastname" },
            { "data": 4, "name": "phones", "orderable": false },
            { "data": 5, "name": "city" },
            { "data": 6, "name": "district" },
            { "data": 7, "name": "offer_status" },
            { "data": 8, "name": "offer_date" },
            { "data": 9, "name": "actions", "orderable": false, "searchable": false }
        ],
        "initComplete": function () {
            // Her sütun için filtreleme input'u ekle
            this.api().columns().every(function (index) {
                var column = this;
                var title = $(column.header()).text();

                // İşlemler sütunu için filtreleme ekleme
                if (index === $('#potentialCustomersTable thead th').length - 1) {
                    return;
                }

                // Header'a filtreleme input'u ekle
                var input = $('<input type="text" placeholder="' + title + ' ara..." class="form-control form-control-sm" style="margin-top: 5px;" />')
                    .appendTo($(column.header()))
                    .on('keyup change clear', function () {
                        if (column.search() !== this.value) {
                            column.search(this.value).draw();
                        }
                    });
            });
        }
    });
});
</script>
<?php $__env->stopPush(); ?>



<?php echo $__env->make('layouts.index', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/crm.umram.online/resources/views/potential_customers/index.blade.php ENDPATH**/ ?>