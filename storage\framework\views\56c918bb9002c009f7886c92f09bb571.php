<?php $__env->startSection('content'); ?>
<!-- Content Header (Page header) -->
<section class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1>Bölge <PERSON>esi</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">Ana Sayfa</a></li>
                    <li class="breadcrumb-item active">Bölge Listesi</li>
                </ol>
            </div>
        </div>
    </div><!-- /.container-fluid -->
</section>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <a href="<?php echo e(route('regions.create')); ?>" class="btn btn-primary">Yeni Bölge <PERSON></a>
                        </h3>

                        <div class="card-tools">
                            <?php if(session('success')): ?>
                                <div class="alert alert-success">
                                    <?php echo e(session('success')); ?>

                                </div>
                            <?php endif; ?>

                            <form method="GET" action="" class="d-flex">
                                <div class="input-group input-group-sm">
                                    <input type="text" name="q" class="form-control float-right" placeholder="bölge adı, açıklama" value="<?php echo e(request('q')); ?>">

                                    <div class="input-group-append">
                                        <button type="submit" class="btn btn-default"><i class="fas fa-search"></i></button>
                                    </div>
                                </div>
                                <?php if(request('q')): ?>
                                    <a href="<?php echo e(route('regions.index')); ?>" class="btn btn-outline-danger">Temizle</a>
                                <?php endif; ?>
                            </form>
                        </div>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body table-responsive p-0" style="height: 100%;">
                        <table id="regionsTable" class="table table-head-fixed text-nowrap table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Bölge Adı</th>
                                    <th>Açıklama</th>
                                    <th>Bayi Sayısı</th>
                                    <th>Durum</th>
                                    <th>Oluşturulma Tarihi</th>
                                    <th>İşlemler</th>
                                </tr>
                            </thead>
                            <tbody>
                            <?php $__currentLoopData = $regions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $region): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e($region->id); ?></td>
                                    <td><?php echo e($region->name); ?></td>
                                    <td><?php echo e(Str::limit($region->description, 50) ?? '-'); ?></td>
                                    <td><?php echo e($region->dealers_count ?? $region->dealers->count()); ?></td>
                                    <td>
                                        <?php if($region->status): ?>
                                            <span class="badge badge-success">Aktif</span>
                                        <?php else: ?>
                                            <span class="badge badge-danger">Pasif</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo e($region->created_at->format('d.m.Y')); ?></td>
                                    <td>
                                        <a href="<?php echo e(route('regions.show', $region->id)); ?>" class="btn btn-info btn-sm" title="Detay">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo e(route('regions.edit', $region->id)); ?>" class="btn btn-warning btn-sm" title="Düzenle">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="<?php echo e(route('regions.destroy', $region->id)); ?>" method="POST" style="display: inline-block;" onsubmit="return confirm('Bu bölgeyi silmek istediğinizden emin misiniz?')">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" class="btn btn-danger btn-sm" title="Sil">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                    <!-- /.card-body -->
                    <div class="card-footer clearfix">
                        <?php echo e($regions->links('pagination::bootstrap-4')); ?>

                    </div>
                </div>
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>
<!-- /.content -->
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    $('#regionsTable').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "<?php echo e(route('regions.datatable')); ?>",
            "type": "GET"
        },
        "language": {
            "url": "https://cdn.datatables.net/plug-ins/1.13.7/i18n/tr.json"
        },
        "pageLength": 25,
        "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "Tümü"]],
        "order": [[0, "desc"]],
        "columnDefs": [
            {
                "targets": -1, // Son sütun (İşlemler)
                "orderable": false,
                "searchable": false
            }
        ],
        "initComplete": function () {
            // Her sütun için filtreleme input'u ekle
            this.api().columns().every(function (index) {
                var column = this;
                var title = $(column.header()).text();

                // İşlemler sütunu için filtreleme ekleme
                if (index === $('#regionsTable thead th').length - 1) {
                    return;
                }

                // Header'a filtreleme input'u ekle
                var input = $('<input type="text" placeholder="' + title + ' ara..." class="form-control form-control-sm" style="margin-top: 5px;" />')
                    .appendTo($(column.header()))
                    .on('keyup change clear', function () {
                        if (column.search() !== this.value) {
                            column.search(this.value).draw();
                        }
                    });
            });
        }
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.index', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/crm.umram.online/resources/views/regions/index.blade.php ENDPATH**/ ?>